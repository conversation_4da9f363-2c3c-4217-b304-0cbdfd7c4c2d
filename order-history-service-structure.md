# Order History Service Structure

## Overview
The Order History service is responsible for tracking, storing, and providing access to order lifecycle events and states. It processes incoming trading messages, maintains order state snapshots and full history, and provides both REST and streaming APIs for order data retrieval.

## Architecture Layers

### 1. Application Layer
```
src/main/java/io/wyden/orderhistory/OrderHistoryApplication.java
```
- **Purpose**: Spring Boot application entry point
- **Responsibilities**: Application configuration, component scanning, startup

### 2. Web Layer (`web/`)
```
src/main/java/io/wyden/orderhistory/web/
├── OrderHistoryController.java
```

#### OrderHistoryController
- **Purpose**: REST API endpoints for order history queries
- **Responsibilities**:
  - Exposes `/order-state-snapshots` and `/order-states` endpoints
  - Handles both streaming and paginated responses
  - Provides protobuf-based API for internal service communication
  - Exception handling for REST endpoints

### 3. Service Layer (`service/`)
```
src/main/java/io/wyden/orderhistory/service/
├── OrderHistoryService.java          # Main business logic
├── OrderStateProcessor.java          # Order state processing
├── inbound/                          # Message handlers
│   ├── ClientRequestHandler.java
│   ├── ClientResponseHandler.java
│   ├── OemsRequestHandler.java
│   └── OemsResponseHandler.java
├── outbound/                         # Event emission
│   └── OrderStateEmitter.java
└── utils/                           # Utility classes
    ├── CancelReplaceRequestMapper.java
    ├── DbUtils.java
    ├── OrderStateComparator.java
    ├── OrderStateMapper.java
    ├── ProtobufUtils.java
    └── sqlquery/                    # SQL query builders
        ├── AbstractQueryBuilder.java
        ├── DeleteQueryBuilder.java
        ├── InsertQueryBuilder.java
        ├── QueryBuilder.java
        ├── SelectQueryBuilder.java
        ├── SqlQuery.java
        └── UpdateQueryBuilder.java
```

#### OrderHistoryService (Main Service)
- **Purpose**: Core business logic for order history management
- **Responsibilities**:
  - Process incoming client requests and responses
  - Process OEMS requests and responses
  - Coordinate order state updates
  - Provide query interfaces for order snapshots and states
  - Handle pagination and search functionality
  - Manage order state lifecycle

#### OrderStateProcessor
- **Purpose**: Order state transformation and persistence coordination
- **Responsibilities**:
  - Process new order states from various sources
  - Merge existing states with updates
  - Coordinate saving snapshots and full states
  - Emit order state changes to downstream systems
  - Handle state comparisons and change detection

#### Inbound Message Handlers
- **ClientRequestHandler**: Processes incoming client order requests
- **ClientResponseHandler**: Processes client order responses and execution reports
- **OemsRequestHandler**: Processes OEMS (Order Execution Management System) requests
- **OemsResponseHandler**: Processes OEMS responses and execution reports

#### Outbound Components
- **OrderStateEmitter**: Publishes order state changes to message queues for real-time streaming

#### Utility Classes
- **DbUtils**: Database query construction and parameter handling
- **OrderStateMapper**: Conversion between entities and protobuf messages
- **OrderStateComparator**: Detects changes in order states
- **ProtobufUtils**: Protobuf message utilities and conversions
- **SQL Query Builders**: Type-safe SQL query construction

### 4. Repository Layer (`repository/`)
```
src/main/java/io/wyden/orderhistory/repository/
├── OrderStateRepository.java              # Interface
├── PostgresOrderStateRepository.java      # Implementation
├── MeteredPostgresOrderStateRepository.java # Metrics wrapper
├── PortfolioProvider.java                 # Portfolio data access
└── RepositoryConfig.java                  # Repository configuration
```

#### OrderStateRepository (Interface)
- **Purpose**: Data access abstraction for order states
- **Responsibilities**: Define CRUD operations for order states and snapshots

#### PostgresOrderStateRepository
- **Purpose**: PostgreSQL implementation of order state persistence
- **Responsibilities**:
  - Execute SQL queries for order state operations
  - Handle complex search queries with multiple predicates
  - Manage both snapshot and full state tables
  - Implement pagination and sorting

#### MeteredPostgresOrderStateRepository
- **Purpose**: Metrics and monitoring wrapper
- **Responsibilities**:
  - Add performance metrics to all database operations
  - Track query execution times and throughput
  - Provide observability for database performance

#### PortfolioProvider
- **Purpose**: Portfolio reference data access
- **Responsibilities**: Retrieve portfolio information for order processing

### 5. Model Layer (`model/`)
```
src/main/java/io/wyden/orderhistory/model/
├── OrderStateEntity.java              # Core order state entity
├── CancelReplaceRequestEntity.java     # Cancel/replace request entity
├── OrderHistorySearchInput.java       # Search criteria
├── SimplePredicateInput.java          # Simple search predicates
├── CollectionPredicateInput.java      # Collection-based predicates
├── DatePredicateInput.java            # Date range predicates
├── PredicateInput.java                # Base predicate interface
└── SortingOrder.java                  # Sorting enumeration
```

#### OrderStateEntity
- **Purpose**: Core domain entity representing order state
- **Responsibilities**:
  - Encapsulate all order state data (IDs, quantities, prices, status, etc.)
  - Provide builder pattern for construction
  - Support both snapshot and historical state representations

#### Search Input Models
- **OrderHistorySearchInput**: Main search criteria container
- **SimplePredicateInput**: Single-field equality/comparison predicates
- **CollectionPredicateInput**: Multi-value predicates (IN clauses)
- **DatePredicateInput**: Date range filtering predicates

### 6. Infrastructure Layer (`infrastructure/`)
```
src/main/java/io/wyden/orderhistory/infrastructure/
├── hazelcast/                          # Caching layer
│   └── HazelcastConfig.java
├── health/                             # Health checks
│   ├── HealthConfiguration.java
│   └── HealthIndicators.java
├── rabbit/                             # Message queue integration
│   ├── TradingMessageConsumer.java
│   ├── RabbitDestinations.java
│   ├── RabbitHealthCheckConfiguration.java
│   └── RabbitWiring.java
└── telemetry/                          # Monitoring and metrics
    ├── Meters.java
    ├── MetricsConfiguration.java
    └── TelemetryConfiguration.java
```

#### Hazelcast Configuration
- **Purpose**: Distributed caching configuration
- **Responsibilities**:
  - Configure Hazelcast client for portfolio and reference data caching
  - Set up near cache for performance optimization
  - Configure eviction policies and TTL settings

#### Message Queue Integration
- **TradingMessageConsumer**: Main message consumer for trading events
- **RabbitDestinations**: Queue and exchange definitions
- **RabbitWiring**: Message routing and handler configuration

#### Health and Monitoring
- **HealthIndicators**: Application health checks for dependencies
- **Meters**: Custom metrics definitions for business and technical metrics
- **TelemetryConfiguration**: OpenTelemetry and metrics setup

## Data Flow

### 1. Inbound Message Processing
```
RabbitMQ → TradingMessageConsumer → Message Handlers → OrderStateProcessor → Repository
```

### 2. Order State Updates
```
OrderStateProcessor → OrderStateComparator → Repository (Save/Update) → OrderStateEmitter
```

### 3. Query Processing
```
REST API → OrderHistoryService → Repository → Response Mapping → Client
```

### 4. Streaming
```
OrderStateEmitter → Message Queue → External Consumers
```

## Key Design Patterns

### 1. Repository Pattern
- Clean separation between business logic and data access
- Interface-based design for testability
- Metrics decoration pattern for observability

### 2. Handler Pattern
- Separate handlers for different message types
- Consistent error handling and logging
- Metrics collection for each handler

### 3. Builder Pattern
- SQL query builders for type-safe query construction
- Entity builders for complex object creation

### 4. Strategy Pattern
- Different processing strategies for new vs. existing orders
- Pluggable predicate handling for search queries

## Database Schema

### Tables
1. **order_state_snapshot**: Current state of each order (one row per order)
2. **order_state**: Complete history of order state changes (multiple rows per order)
3. **cancel_replace_request_cache**: Temporary storage for cancel/replace operations

### Key Indexes
- Primary keys on order_id
- Indexes on portfolio_id, venue_account_id for filtering
- Composite indexes on updated_at for pagination
- Sequence_number index for ordering historical states

## Integration Points

### External Dependencies
- **PostgreSQL**: Primary data store
- **Hazelcast**: Distributed caching for reference data
- **RabbitMQ**: Message queue for event processing and streaming
- **Portfolio Service**: Reference data for portfolio information

### API Consumers
- **REST API Server**: GraphQL and REST endpoints
- **Management Server**: Administrative interfaces
- **Real-time Streaming**: WebSocket and reactive streams

## Performance Considerations

### Current Bottlenecks
1. **Synchronous message processing**: Blocks on database operations
2. **Dual storage**: Both snapshot and history tables create write overhead
3. **Complex queries**: Multi-predicate searches can be slow
4. **Memory usage**: Unbounded streaming collections

### Optimization Opportunities
1. **Async processing**: Non-blocking message handling
2. **Query optimization**: Better indexing and query structure
3. **Caching**: Intelligent caching of frequently accessed data
4. **Batch processing**: Group related operations

This structure provides a solid foundation but requires the refactoring outlined in the previous plan to address performance, memory, and security issues.
